from fastapi import APIRouter

from app.models.predict_request import PredictRequest

router = APIRouter()

@router.post("/predict")
async def get_therapist_response(r: PredictRequest):
    print("Received S3 key:", r.s3_key)
    print("Condition checks for support:", r.parameters.condition.support)
    print("Artwork Title:", r.art_details.title)
    print("Is in frame?", r.artpiece_enclosed_in_frame)

    return {"status": "success", "message": "Analysis started.", "details": r.model_dump()}