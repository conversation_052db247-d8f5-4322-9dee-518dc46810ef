from pydantic import BaseModel, Field
from typing import List, Optional, Any

class Condition(BaseModel):
    """
    Defines the expected condition parameters for different parts of the artwork.
    """
    support: List[str] = Field(..., description="List of condition checks for the artwork support.")
    frame: List[str] = Field(..., description="List of condition checks for the artwork frame.")

class Parameters(BaseModel):
    """
    Contains the analysis parameters for the artwork.
    """
    condition: Condition
    type: str

class ArtDetails(BaseModel):
    """
    Holds the metadata and details about the piece of art.
    """
    title: str
    artist: str
    medium: str
    dimensions: str
    user_id: str

class PredictRequest(BaseModel):
    """
    Main model for the art analysis request body. This is the model your
    FastAPI endpoint will expect.
    """
    s3_key: str
    bucket_name: str
    parameters: Parameters
    art_details: ArtDetails
    output_s3_path: str
    artpiece_enclosed_in_frame: bool
    bounding_box: Optional[Any]